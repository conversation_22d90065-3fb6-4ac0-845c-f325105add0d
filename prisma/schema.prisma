// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema
// เฮียสั่งมา (HiaSangMa) - SaaS ระบบจัดการงานระดับผู้บริหารสำหรับ SME ไทย

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model UserRole {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  isOwner     <PERSON>olean  @default(false) @map("is_owner")
  isAd<PERSON>  @default(false) @map("is_admin")
  isMember    Boolean  @default(false) @map("is_member")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  users       User[]

  @@map("user_roles")
}

model User {
  id           Int       @id @default(autoincrement())
  email        String    @unique
  passwordHash String    @map("password_hash")
  firstName    String    @map("first_name")
  lastName     String    @map("last_name")
  phone        String?
  imageUrl     String?   @map("image_url")
  userRoleId   Int       @map("user_role_id")
  isOwner      <PERSON>olean   @default(false) @map("is_owner") // User type validation
  isAdmin      Boolean   @default(false) @map("is_admin") // User type validation
  isMember     Boolean   @default(false) @map("is_member") // User type validation
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")
  deletedAt    DateTime? @map("deleted_at")

  // Relations
  userRole           UserRole           @relation(fields: [userRoleId], references: [id])
  ownedOrganizations Organization[]
  departmentMembers  DepartmentMember[]
  createdTasks       Task[]             @relation("CreatedByUser")
  taskAssignments    TaskAssignment[] // Tasks assigned to this user
  assignedByMe       TaskAssignment[]   @relation("AssignedBy") // Tasks assigned by this user
  taskProgresses     TaskProgress[]
  pointTransactions  PointTransaction[]
  userNotifications  UserNotification[]
  chatUsers          ChatUser[]
  chatMessages       ChatMessage[]

  @@map("users")
}

model Organization {
  id          Int      @id @default(autoincrement())
  name        String
  description String?  @db.Text
  imageUrl    String?  @map("image_url")
  ownerUserId Int      @map("owner_user_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  owner       User         @relation(fields: [ownerUserId], references: [id])
  departments Department[]
  tasks       Task[]
  chats       Chat[]

  @@map("organizations")
}

model Department {
  id             Int      @id @default(autoincrement())
  name           String
  description    String?  @db.Text
  organizationId Int      @map("organization_id")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  organization Organization       @relation(fields: [organizationId], references: [id])
  members      DepartmentMember[]
  tasks        Task[]
  chats        Chat[]

  @@map("departments")
}

model DepartmentMember {
  id           Int      @id @default(autoincrement())
  userId       Int      @map("user_id")
  departmentId Int      @map("department_id")
  isLeader     Boolean  @default(false) @map("is_leader")
  joinedAt     DateTime @map("joined_at")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  user       User       @relation(fields: [userId], references: [id])
  department Department @relation(fields: [departmentId], references: [id])

  @@map("department_members")
}

model TaskStatus {
  id              Int      @id @default(autoincrement())
  name            String
  displayName     String   @map("display_name")
  color           String
  description     String?  @db.Text
  index           Int      @default(0) @map("index") // For sorting statuses in UI
  isMemberDisplay Boolean  @default(true) @map("is_member_display") // Control visibility based on user role
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  tasks                 Task[]
  fromStatusTransitions TaskStatusTransition[] @relation("FromStatus")
  toStatusTransitions   TaskStatusTransition[] @relation("ToStatus")

  @@map("task_status")
}

model TaskStatusTransition {
  id           Int      @id @default(autoincrement())
  fromStatusId Int      @map("from_status_id")
  toStatusId   Int      @map("to_status_id")
  allowOwner   Boolean  @default(true) @map("allow_owner") // Owners can do all transitions by default
  allowAdmin   Boolean  @default(false) @map("allow_admin")
  allowMember  Boolean  @default(false) @map("allow_member")
  description  String?  @db.Text
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  fromStatus TaskStatus @relation("FromStatus", fields: [fromStatusId], references: [id])
  toStatus   TaskStatus @relation("ToStatus", fields: [toStatusId], references: [id])

  @@unique([fromStatusId, toStatusId])
  @@map("task_status_transitions")
}

model Task {
  id              Int       @id @default(autoincrement())
  taskTitle       String    @map("task_title")
  taskDescription String?   @map("task_description") @db.Text
  createdByUserId Int       @map("created_by_user_id")
  statusId        Int       @map("status_id")
  organizationId  Int       @map("organization_id")
  departmentId    Int       @map("department_id")
  points          Int?
  isClaimPoint    Boolean   @default(false) @map("is_claim_point")
  dueDate         DateTime? @map("due_date")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  // Relations
  createdByUser     User               @relation("CreatedByUser", fields: [createdByUserId], references: [id])
  status            TaskStatus         @relation(fields: [statusId], references: [id])
  organization      Organization       @relation(fields: [organizationId], references: [id])
  department        Department         @relation(fields: [departmentId], references: [id])
  taskAssignments   TaskAssignment[] // Many-to-many relation with users
  taskProgresses    TaskProgress[]
  pointTransactions PointTransaction[]
  chats             Chat[]

  @@map("tasks")
}

model TaskAssignment {
  id         Int      @id @default(autoincrement())
  taskId     Int      @map("task_id")
  userId     Int      @map("user_id")
  assignedAt DateTime @default(now()) @map("assigned_at")
  assignedBy Int?     @map("assigned_by") // User who made the assignment
  isLeader   Boolean  @default(false) @map("is_leader") // Indicates if this user is the task leader
  isActive   Boolean  @default(true) @map("is_active")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // Relations
  task           Task  @relation(fields: [taskId], references: [id], onDelete: Cascade)
  user           User  @relation(fields: [userId], references: [id])
  assignedByUser User? @relation("AssignedBy", fields: [assignedBy], references: [id])

  @@unique([taskId, userId])
  @@map("task_assignments")
}

model TaskProgressType {
  id          Int      @id @default(autoincrement())
  name        String
  displayName String   @map("display_name")
  color       String
  description String?  @db.Text
  isOwner     Boolean  @default(true) @map("is_owner") // Owner can see and use this progress type
  isAdmin     Boolean  @default(true) @map("is_admin") // Admin can see and use this progress type
  isMember    Boolean  @default(true) @map("is_member") // Member can see and use this progress type
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  taskProgresses TaskProgress[]

  @@map("task_progress_types")
}

model TaskProgress {
  id                  Int      @id @default(autoincrement())
  taskId              Int      @map("task_id")
  updatedByUserId     Int      @map("updated_by_user_id")
  progressTypeId      Int      @map("progress_type_id")
  progressDescription String   @map("progress_description") @db.Text
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  // Relations
  task          Task             @relation(fields: [taskId], references: [id])
  updatedByUser User             @relation(fields: [updatedByUserId], references: [id])
  progressType  TaskProgressType @relation(fields: [progressTypeId], references: [id])

  @@map("task_progress")
}

model PointTransaction {
  id          Int      @id @default(autoincrement())
  userId      Int      @map("user_id")
  taskId      Int      @map("task_id")
  pointAmount Int      @map("point_amount")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  user User @relation(fields: [userId], references: [id])
  task Task @relation(fields: [taskId], references: [id])

  @@map("point_transactions")
}

model NotificationType {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  displayName String   @map("display_name")
  description String?  @db.Text
  template    String   @db.Text // Template for notification message with placeholders
  color       String? // Color code for UI display
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  notifications Notification[]

  @@map("notification_types")
}

model Notification {
  id         Int      @id @default(autoincrement())
  typeId     Int      @map("type_id")
  title      String
  content    String   @db.Text
  data       Json? // Additional data in JSON format
  entityType String?  @map("entity_type") // e.g., "task", "department"
  entityId   Int?     @map("entity_id") // ID of the related entity
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // Relations
  type              NotificationType   @relation(fields: [typeId], references: [id])
  userNotifications UserNotification[]

  @@map("notifications")
}

model UserNotification {
  id             Int       @id @default(autoincrement())
  userId         Int       @map("user_id")
  notificationId Int       @map("notification_id")
  isRead         Boolean   @default(false) @map("is_read")
  readAt         DateTime? @map("read_at")
  isArchived     Boolean   @default(false) @map("is_archived")
  archivedAt     DateTime? @map("archived_at")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")

  // Relations
  user         User         @relation(fields: [userId], references: [id])
  notification Notification @relation(fields: [notificationId], references: [id])

  @@unique([userId, notificationId])
  @@map("user_notifications")
}

model Chat {
  id             Int       @id @default(autoincrement())
  name           String? // Optional name for group chats
  chatType       ChatType  @map("chat_type")
  organizationId Int?      @map("organization_id") // Required for organization/department chats
  departmentId   Int?      @map("department_id") // Required for department chats
  taskId         Int?      @map("task_id") // Required for task chats
  isActive       Boolean   @default(true) @map("is_active")
  lastMessageAt  DateTime? @map("last_message_at")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")

  // Relations
  organization Organization? @relation(fields: [organizationId], references: [id])
  department   Department?   @relation(fields: [departmentId], references: [id])
  task         Task?         @relation(fields: [taskId], references: [id])
  chatUsers    ChatUser[]
  messages     ChatMessage[]

  @@map("chats")
}

model ChatUser {
  id        Int      @id @default(autoincrement())
  chatId    Int      @map("chat_id")
  userId    Int      @map("user_id")
  isAdmin   Boolean  @default(false) @map("is_admin") // Can manage chat (add/remove users, etc.)
  joinedAt  DateTime @default(now()) @map("joined_at")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  chat Chat @relation(fields: [chatId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id])

  @@unique([chatId, userId])
  @@map("chat_users")
}

model ChatMessage {
  id            Int           @id @default(autoincrement())
  chatId        Int           @map("chat_id")
  userId        Int           @map("user_id")
  content       String        @db.Text
  messageType   MessageType   @default(TEXT) @map("message_type")
  messageStatus MessageStatus @default(SENDING) @map("message_status")
  createdAt     DateTime      @default(now()) @map("created_at")
  updatedAt     DateTime      @updatedAt @map("updated_at")

  // Relations
  chat Chat @relation(fields: [chatId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id])

  @@map("chat_messages")
}

enum ChatType {
  PRIVATE      @map("private")
  TASK         @map("task")
  DEPARTMENT   @map("department")
  ORGANIZATION @map("organization")

  @@map("chat_types")
}

enum MessageType {
  TEXT    @map("text")
  IMAGE   @map("image")
  FILE    @map("file")
  STICKER @map("sticker")

  @@map("message_types")
}

enum MessageStatus {
  SENDING   @map("sending")
  DELIVERED @map("delivered")
  READ      @map("read")
  FAILED    @map("failed")

  @@map("message_status")
}
