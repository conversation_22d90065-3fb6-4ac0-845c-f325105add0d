// Test script for Organization Admin API
const BASE_URL = 'http://localhost:3000/api/v1';

// Test data
const testData = {
  // You'll need to replace these with actual values from your database
  organizationId: 1, // Replace with actual organization ID
  adminUserId: 2,    // Replace with actual user ID that has admin role
  ownerToken: 'your-owner-jwt-token', // Replace with actual owner token
};

async function testAPI(endpoint, method = 'GET', body = null, token = null) {
  const headers = {
    'Content-Type': 'application/json',
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  const config = {
    method,
    headers,
  };

  if (body) {
    config.body = JSON.stringify(body);
  }

  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, config);
    const data = await response.json();
    
    console.log(`\n=== ${method} ${endpoint} ===`);
    console.log(`Status: ${response.status}`);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    return { status: response.status, data };
  } catch (error) {
    console.error(`Error testing ${endpoint}:`, error);
    return { error };
  }
}

async function runTests() {
  console.log('🧪 Testing Organization Admin API');
  console.log('=====================================');

  // Test 1: Get available users for admin assignment
  console.log('\n📋 Test 1: Get available users for admin assignment');
  await testAPI(
    `/organization-admin/available-users?organizationId=${testData.organizationId}`,
    'GET',
    null,
    testData.ownerToken
  );

  // Test 2: Get organization admins (should be empty initially)
  console.log('\n📋 Test 2: Get organization admins');
  await testAPI(
    `/organization-admin?organizationId=${testData.organizationId}`,
    'GET',
    null,
    testData.ownerToken
  );

  // Test 3: Add organization admin
  console.log('\n📋 Test 3: Add organization admin');
  await testAPI(
    '/organization-admin',
    'POST',
    {
      organizationId: testData.organizationId,
      userId: testData.adminUserId,
    },
    testData.ownerToken
  );

  // Test 4: Get organization admins (should show the added admin)
  console.log('\n📋 Test 4: Get organization admins after adding');
  await testAPI(
    `/organization-admin?organizationId=${testData.organizationId}`,
    'GET',
    null,
    testData.ownerToken
  );

  // Test 5: Test permission system - try to access with admin token
  console.log('\n📋 Test 5: Test admin access to organizations');
  await testAPI(
    '/organization',
    'GET',
    null,
    'admin-jwt-token' // Replace with actual admin token
  );

  // Test 6: Test updated /me endpoint
  console.log('\n📋 Test 6: Test updated /me endpoint');
  await testAPI(
    '/me',
    'GET',
    null,
    testData.ownerToken
  );

  // Test 7: Remove organization admin
  console.log('\n📋 Test 7: Remove organization admin');
  await testAPI(
    '/organization-admin?id=1', // Replace with actual admin ID
    'DELETE',
    null,
    testData.ownerToken
  );

  console.log('\n✅ All tests completed!');
  console.log('\n📝 Manual testing steps:');
  console.log('1. Login as organization owner');
  console.log('2. Go to Settings > Organizations');
  console.log('3. Click on an organization');
  console.log('4. Click "Manage Admins" button');
  console.log('5. Try adding a user as admin');
  console.log('6. Verify the admin appears in the list');
  console.log('7. Try removing the admin');
  console.log('8. Login as the admin user and verify they can access the organization');
}

// Instructions for running the test
console.log('🔧 Setup Instructions:');
console.log('1. Update testData object with actual values from your database');
console.log('2. Get JWT tokens for owner and admin users');
console.log('3. Run: node test-organization-admin.js');
console.log('4. Check the API responses');

// Uncomment the line below to run tests
// runTests();

// Helper function to get JWT token (for manual testing)
function getJWTInstructions() {
  console.log('\n🔑 To get JWT tokens:');
  console.log('1. Login via /api/v1/login endpoint');
  console.log('2. Copy the token from the response');
  console.log('3. Use it in the Authorization header: Bearer <token>');
  console.log('\nExample login request:');
  console.log('POST /api/v1/login');
  console.log('Body: { "email": "<EMAIL>", "password": "password" }');
}

getJWTInstructions();

// Export for use in other test files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testAPI, runTests };
}
