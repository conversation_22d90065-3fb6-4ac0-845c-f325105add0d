# S3 File Upload Service - Complete Guide

## Overview

This comprehensive S3 file upload service provides secure, authenticated file management for the HiaSangMa application. It supports multiple upload methods, file type validation, user isolation, and progress tracking.

### Key Features
- 🔐 JWT Authentication required
- 👤 User-isolated file storage
- 📁 Multiple file type support
- 📊 Upload progress tracking
- 🗑️ Secure file deletion
- ⚡ Direct S3 uploads
- 🎯 Drag & drop interface

## Quick Start

### 1. Environment Configuration

Add these environment variables to your `.env.local` file:

```bash
# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1
AWS_S3_BUCKET_NAME=your-bucket-name

# Frontend Environment Variables
NEXT_PUBLIC_AWS_S3_BUCKET_NAME=your-bucket-name
NEXT_PUBLIC_AWS_REGION=us-east-1

# JWT Configuration
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=24h
```

### 2. Install Dependencies

```bash
npm install @aws-sdk/client-s3 @aws-sdk/s3-request-presigner
```

## AWS S3 Bucket Setup

### Step 1: Create S3 Bucket

1. Log into AWS Console and navigate to S3
2. Click "Create bucket"
3. Choose a unique bucket name (e.g., `hiasangma-files-prod`)
4. Select your preferred region (must match `AWS_REGION` in env)
5. **Important**: Uncheck "Block all public access" if you need public file access
6. Enable versioning (recommended)
7. Create the bucket

### Step 2: Configure CORS Policy

In your S3 bucket, go to **Permissions** → **CORS** and add:

```json
[
    {
        "AllowedHeaders": ["*"],
        "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
        "AllowedOrigins": [
            "http://localhost:3000",
            "http://localhost:3001", 
            "https://yourdomain.com",
            "https://*.yourdomain.com"
        ],
        "ExposeHeaders": ["ETag", "x-amz-meta-*"],
        "MaxAgeSeconds": 3000
    }
]
```

### Step 3: Create IAM User & Policy

1. Go to **IAM** → **Users** → **Create user**
2. Username: `hiasangma-s3-user`
3. Attach the following custom policy:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "S3ObjectAccess",
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject",
                "s3:PutObjectAcl",
                "s3:GetObjectAcl"
            ],
            "Resource": "arn:aws:s3:::your-bucket-name/*"
        },
        {
            "Sid": "S3BucketAccess",
            "Effect": "Allow",
            "Action": [
                "s3:ListBucket",
                "s3:GetBucketLocation"
            ],
            "Resource": "arn:aws:s3:::your-bucket-name"
        }
    ]
}
```

4. Create access keys for this user
5. Copy the **Access Key ID** and **Secret Access Key** to your `.env.local`

## File Type Configurations

The service supports multiple file categories with specific size limits and allowed formats:

| Category | Max Size | Allowed Formats | Use Case |
|----------|----------|-----------------|----------|
| **images** | 10MB | JPEG, PNG, GIF, WebP | General image uploads, galleries |
| **documents** | 50MB | PDF, DOC, DOCX, XLS, XLSX, TXT | Document sharing, reports |
| **avatars** | 5MB | JPEG, PNG, WebP | Profile pictures, user avatars |
| **chat** | 25MB | Images + PDF + TXT | Chat attachments, file sharing |

### File Organization Structure

Files are organized in S3 with the following structure:
```
your-bucket/
├── user-123/
│   ├── images/
│   │   └── 1641234567890-image.jpg
│   ├── documents/
│   │   └── 1641234567890-document.pdf
│   ├── avatars/
│   │   └── 1641234567890-avatar.png
│   └── chat/
│       └── 1641234567890-file.txt
└── user-456/
    └── ...
```

## API Endpoints Overview

| Endpoint | Method | Purpose | Authentication |
|----------|--------|---------|----------------|
| `/api/v1/upload` | POST | Direct server upload | JWT Required |
| `/api/v1/upload/delete` | DELETE | Delete file | JWT Required |

## Detailed API Usage Guide

### 1. Direct File Upload API

**Endpoint**: `POST /api/v1/upload`

**Headers**:
```
Authorization: Bearer <jwt_token>
Content-Type: multipart/form-data
```

**Request Body** (FormData):
- `file`: File object
- `fileType`: One of `images`, `documents`, `avatars`, `chat`

**Example with JavaScript**:
```javascript
const uploadFile = async (file, fileType) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('fileType', fileType);

  const response = await fetch('/api/v1/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    },
    body: formData
  });

  const result = await response.json();
  return result;
};

// Usage
const fileInput = document.getElementById('fileInput');
const file = fileInput.files[0];
const result = await uploadFile(file, 'images');
console.log('Upload result:', result);
```

**Success Response**:
```json
{
  "success": true,
  "data": {
    "s3Key": "user-123/images/1641234567890-image.jpg",
    "s3Url": "https://your-bucket.s3.amazonaws.com/user-123/images/1641234567890-image.jpg",
    "fileName": "image.jpg",
    "fileSize": 1024000,
    "fileType": "images",
    "uploadedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

### 2. File Deletion API

**Endpoint**: `DELETE /api/v1/upload/delete`

**Headers**:
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body**:
```json
{
  "s3Key": "user-123/images/1641234567890-image.jpg"
}
```

**Example**:
```javascript
const deleteFile = async (s3Key) => {
  const response = await fetch('/api/v1/upload/delete', {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ s3Key })
  });

  return await response.json();
};

// Usage
const result = await deleteFile('user-123/images/1641234567890-image.jpg');
console.log('Delete result:', result);
```

**Success Response**:
```json
{
  "success": true,
  "message": "File deleted successfully"
}
```

## React Component Usage Examples

### Using the FileUpload Component

```tsx
import { FileUpload } from '@/components/FileUpload';

function MyComponent() {
  const handleUploadComplete = (files) => {
    console.log('Uploaded files:', files);
    // Handle successful uploads
    files.forEach(file => {
      console.log(`Uploaded: ${file.fileName} to ${file.s3Url}`);
    });
  };

  const handleUploadError = (error) => {
    console.error('Upload failed:', error);
    // Handle upload errors
  };

  return (
    <div className="upload-container">
      <h3>Upload Images</h3>
      <FileUpload
        fileType="images"
        multiple={true}
        maxFiles={5}
        onUploadComplete={handleUploadComplete}
        onUploadError={handleUploadError}
        className="custom-upload-area"
      />
    </div>
  );
}
```

### Using the useFileUpload Hook

```tsx
import { useFileUpload } from '@/hooks/useFileUpload';
import { useState } from 'react';

function AdvancedUploadComponent() {
  const { uploadFile, uploads, isUploading, clearUploads } = useFileUpload();
  const [uploadedFiles, setUploadedFiles] = useState([]);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    
    for (const file of files) {
      try {
        const result = await uploadFile(file, 'images');
        if (result.success) {
          console.log('File uploaded:', result.data);
          setUploadedFiles(prev => [...prev, result.data]);
        }
      } catch (error) {
        console.error('Upload failed:', error);
      }
    }
  };

  const handleClearUploads = () => {
    clearUploads();
    setUploadedFiles([]);
  };

  return (
    <div className="upload-component">
      <input
        type="file"
        multiple
        accept="image/*"
        onChange={handleFileSelect}
        disabled={isUploading}
      />
      
      {/* Upload Progress */}
      {uploads.length > 0 && (
        <div className="upload-progress">
          <h4>Upload Progress:</h4>
          {uploads.map(upload => (
            <div key={upload.fileName} className="progress-item">
              <span>{upload.fileName}</span>
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ width: `${upload.progress}%` }}
                />
              </div>
              <span>{upload.progress}%</span>
              {upload.error && (
                <span className="error">Error: {upload.error}</span>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <div className="uploaded-files">
          <h4>Uploaded Files:</h4>
          {uploadedFiles.map((file, index) => (
            <div key={index} className="uploaded-file">
              <img src={file.s3Url} alt={file.fileName} width="100" />
              <p>{file.fileName}</p>
              <p>Size: {(file.fileSize / 1024).toFixed(2)} KB</p>
            </div>
          ))}
          <button onClick={handleClearUploads}>Clear All</button>
        </div>
      )}
    </div>
  );
}
```

### Using the S3 Service Directly

```tsx
import { s3Service } from '@/services/s3Service';

// Example: Complete file management class
class FileManager {
  async uploadDocument(file: File) {
    try {
      // Direct upload method
      const result = await s3Service.uploadFile(file, 'documents');
      
      if (result.success) {
        console.log('Document uploaded:', result.data);
        return result.data;
      }
    } catch (error) {
      console.error('Upload failed:', error);
      throw error;
    }
  }

  async uploadImageWithProgress(file: File, onProgress?: (progress: number) => void) {
    try {
      // Direct upload to server
      const result = await s3Service.uploadFile(file, 'images');
      
      if (result.success) {
        console.log('Image uploaded:', result.data);
        return result.data;
      }
    } catch (error) {
      console.error('Upload failed:', error);
      throw error;
    }
  }

  async deleteFile(s3Key: string) {
    try {
      const result = await s3Service.deleteFile(s3Key);
      
      if (result.success) {
        console.log('File deleted successfully');
        return true;
      }
    } catch (error) {
      console.error('Delete failed:', error);
      throw error;
    }
  }

  async uploadMultipleFiles(files: File[], fileType: string) {
    const results = [];
    
    for (const file of files) {
      try {
        const result = await s3Service.uploadFile(file, fileType);
        results.push(result);
      } catch (error) {
        console.error(`Failed to upload ${file.name}:`, error);
        results.push({ success: false, error: error.message, fileName: file.name });
      }
    }
    
    return results;
  }
}

// Usage examples
const fileManager = new FileManager();

// Upload a single document
const documentFile = new File(['content'], 'document.pdf', { type: 'application/pdf' });
const uploadedDoc = await fileManager.uploadDocument(documentFile);

// Upload image with progress
const imageFile = new File(['content'], 'image.jpg', { type: 'image/jpeg' });
const uploadedImage = await fileManager.uploadImageWithProgress(
  imageFile,
  (progress) => {
    console.log(`Upload progress: ${progress}%`);
    // Update UI progress bar
  }
);

// Delete a file
await fileManager.deleteFile('user-123/images/1641234567890-image.jpg');

// Upload multiple files
const files = [file1, file2, file3];
const results = await fileManager.uploadMultipleFiles(files, 'images');
console.log('Upload results:', results);
```

## Security Features

- 🔐 **JWT Authentication**: All endpoints require valid JWT tokens
- 👤 **User Isolation**: Files stored under `user-{id}/` prefix for complete separation
- 📋 **File Validation**: Strict file type and size validation
- 🛡️ **Access Control**: Users can only access/delete their own files
- 🔍 **Input Sanitization**: File names and paths are sanitized
- 🚫 **CORS Protection**: Configured CORS policies prevent unauthorized access

## Error Handling

### Common Error Responses

**Authentication Error (401)**:
```json
{
  "success": false,
  "error": "Unauthorized",
  "message": "Invalid or missing JWT token"
}
```

**File Too Large (413)**:
```json
{
  "success": false,
  "error": "File too large",
  "message": "File size exceeds 10MB limit for images",
  "maxSize": 10485760
}
```

**Invalid File Type (400)**:
```json
{
  "success": false,
  "error": "Invalid file type",
  "message": "File type 'application/exe' not allowed for images",
  "allowedTypes": ["image/jpeg", "image/png", "image/gif", "image/webp"]
}
```

### Error Handling in Code

```javascript
const handleFileUpload = async (file, fileType) => {
  try {
    const result = await s3Service.uploadFile(file, fileType);
    
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.message || 'Upload failed');
    }
  } catch (error) {
    // Handle specific error types
    if (error.message.includes('File too large')) {
      alert('File is too large. Please choose a smaller file.');
    } else if (error.message.includes('Invalid file type')) {
      alert('File type not supported. Please choose a different file.');
    } else if (error.message.includes('Unauthorized')) {
      // Redirect to login
      window.location.href = '/login';
    } else {
      alert('Upload failed. Please try again.');
    }
    
    console.error('Upload error:', error);
    throw error;
  }
};
```

## Best Practices

### 1. File Validation
```javascript
// Client-side validation before upload
const validateFile = (file, fileType) => {
  const validations = {
    images: {
      maxSize: 10 * 1024 * 1024,
      types: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    },
    documents: {
      maxSize: 50 * 1024 * 1024,
      types: ['application/pdf', 'application/msword', 'text/plain']
    }
  };
  
  const config = validations[fileType];
  if (!config) {
    throw new Error(`Invalid file type: ${fileType}`);
  }
  
  if (file.size > config.maxSize) {
    throw new Error(`File too large: ${file.size} bytes`);
  }
  
  if (!config.types.includes(file.type)) {
    throw new Error(`Invalid file format: ${file.type}`);
  }
  
  return true;
};
```

### 2. Progress Tracking
```javascript
// Direct upload implementation
const uploadFile = async (file, fileType) => {
  const result = await s3Service.uploadFile(file, fileType);
  return result;
};
```

### 3. Error Recovery
```javascript
// Implement retry logic for failed uploads
const uploadWithRetry = async (file, fileType, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await s3Service.uploadFile(file, fileType);
    } catch (error) {
      console.log(`Upload attempt ${attempt} failed:`, error);
      
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Wait before retry (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
};
```

---

## Summary

This S3 file upload service provides a complete, secure, and scalable solution for file management in your HiaSangMa application. Key benefits:

- ✅ **Secure**: JWT authentication and user isolation
- ✅ **Flexible**: Multiple upload methods and file types
- ✅ **Scalable**: Direct S3 uploads reduce server load
- ✅ **User-friendly**: Progress tracking and error handling
- ✅ **Production-ready**: Comprehensive error handling and validation

For additional support or custom implementations, refer to the individual service files in `/src/lib/`, `/src/services/`, and `/src/components/`.
