'use client';

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { getCookie } from 'cookies-next';
import { appTheme as settingsTheme } from '@/app/theme';
import SettingsLayout from '../../../components/SettingsLayout';
import { 
  ArrowLeft, 
  Plus, 
  UserCheck, 
  UserX, 
  Search, 
  Loader2,
  AlertCircle,
  Users
} from 'lucide-react';

const AdminContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.xl};
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: ${settingsTheme.spacing.xl};
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: ${settingsTheme.spacing.md};
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.md};
`;

const BackButton = styled(Link)`
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.xs};
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  background-color: ${settingsTheme.colors.background.light};
  color: ${settingsTheme.colors.text.secondary};
  text-decoration: none;
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${settingsTheme.colors.background.lighter};
    color: ${settingsTheme.colors.text.primary};
  }
`;

const Title = styled.h1`
  font-size: ${settingsTheme.typography.fontSizes['2xl']};
  font-weight: ${settingsTheme.typography.fontWeights.bold};
  color: ${settingsTheme.colors.text.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.sm};
`;

const AddButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.xs};
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  background-color: ${settingsTheme.colors.primary};
  color: white;
  border: none;
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${settingsTheme.colors.primaryHover};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const SearchContainer = styled.div`
  position: relative;
  width: 100%;
  max-width: 400px;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  padding-left: 40px;
  border: 1px solid ${settingsTheme.colors.border};
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  background-color: ${settingsTheme.colors.background.main};
  color: ${settingsTheme.colors.text.primary};

  &:focus {
    outline: none;
    border-color: ${settingsTheme.colors.primary};
    box-shadow: 0 0 0 3px ${settingsTheme.colors.primary}20;
  }

  &::placeholder {
    color: ${settingsTheme.colors.text.tertiary};
  }
`;

const SearchIcon = styled(Search)`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: ${settingsTheme.colors.text.tertiary};
  width: 16px;
  height: 16px;
`;

const AdminGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: ${settingsTheme.spacing.lg};
  width: 100%;
`;

const AdminCard = styled.div`
  background-color: ${settingsTheme.colors.background.main};
  border: 1px solid ${settingsTheme.colors.border};
  border-radius: ${settingsTheme.borderRadius.lg};
  padding: ${settingsTheme.spacing.lg};
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    border-color: ${settingsTheme.colors.primary};
    box-shadow: ${settingsTheme.shadows.md};
  }
`;

const AdminInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.md};
  margin-bottom: ${settingsTheme.spacing.md};
`;

const AdminAvatar = styled.div`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: ${settingsTheme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: ${settingsTheme.typography.fontWeights.bold};
  font-size: ${settingsTheme.typography.fontSizes.lg};
`;

const AdminImage = styled.img`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
`;

const AdminDetails = styled.div`
  flex: 1;
`;

const AdminName = styled.h3`
  font-size: ${settingsTheme.typography.fontSizes.base};
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  color: ${settingsTheme.colors.text.primary};
  margin: 0 0 ${settingsTheme.spacing.xs} 0;
`;

const AdminEmail = styled.p`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.secondary};
  margin: 0;
`;

const AdminMeta = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.xs};
  margin-bottom: ${settingsTheme.spacing.md};
`;

const MetaItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: ${settingsTheme.typography.fontSizes.sm};
`;

const MetaLabel = styled.span`
  color: ${settingsTheme.colors.text.secondary};
`;

const MetaValue = styled.span`
  color: ${settingsTheme.colors.text.primary};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
`;

const AdminActions = styled.div`
  display: flex;
  gap: ${settingsTheme.spacing.sm};
`;

const ActionButton = styled.button<{ variant?: 'danger' }>`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${settingsTheme.spacing.xs};
  padding: ${settingsTheme.spacing.sm};
  border: 1px solid ${props => props.variant === 'danger' ? settingsTheme.colors.error : settingsTheme.colors.border};
  background-color: ${props => props.variant === 'danger' ? settingsTheme.colors.error + '10' : settingsTheme.colors.background.light};
  color: ${props => props.variant === 'danger' ? settingsTheme.colors.error : settingsTheme.colors.text.primary};
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${props => props.variant === 'danger' ? settingsTheme.colors.error + '20' : settingsTheme.colors.background.lighter};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: ${settingsTheme.spacing.xl};
`;

const ErrorContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.sm};
  padding: ${settingsTheme.spacing.md};
  background-color: ${settingsTheme.colors.error}10;
  border: 1px solid ${settingsTheme.colors.error}30;
  border-radius: ${settingsTheme.borderRadius.md};
  color: ${settingsTheme.colors.error};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${settingsTheme.spacing.xl};
  text-align: center;
  color: ${settingsTheme.colors.text.secondary};
`;

const EmptyIcon = styled(Users)`
  width: 64px;
  height: 64px;
  margin-bottom: ${settingsTheme.spacing.md};
  opacity: 0.5;
`;

const EmptyTitle = styled.h3`
  font-size: ${settingsTheme.typography.fontSizes.lg};
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  color: ${settingsTheme.colors.text.primary};
  margin: 0 0 ${settingsTheme.spacing.sm} 0;
`;

const EmptyDescription = styled.p`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.secondary};
  margin: 0;
  max-width: 400px;
`;

// Modal Styles
const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${settingsTheme.spacing.md};
`;

const ModalContent = styled.div`
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  padding: ${settingsTheme.spacing.xl};
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: ${settingsTheme.shadows.lg};
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const ModalTitle = styled.h2`
  font-size: ${settingsTheme.typography.fontSizes.xl};
  font-weight: ${settingsTheme.typography.fontWeights.bold};
  color: ${settingsTheme.colors.text.primary};
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: ${settingsTheme.typography.fontSizes.xl};
  color: ${settingsTheme.colors.text.secondary};
  cursor: pointer;
  padding: ${settingsTheme.spacing.xs};
  border-radius: ${settingsTheme.borderRadius.sm};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${settingsTheme.colors.background.light};
    color: ${settingsTheme.colors.text.primary};
  }
`;

const ModalActions = styled.div`
  display: flex;
  gap: ${settingsTheme.spacing.md};
  justify-content: flex-end;
  margin-top: ${settingsTheme.spacing.lg};
`;

const ModalButton = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.lg};
  border: 1px solid ${props => props.variant === 'primary' ? settingsTheme.colors.primary : settingsTheme.colors.border};
  background-color: ${props => props.variant === 'primary' ? settingsTheme.colors.primary : settingsTheme.colors.background.light};
  color: ${props => props.variant === 'primary' ? 'white' : settingsTheme.colors.text.primary};
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.xs};

  &:hover {
    background-color: ${props => props.variant === 'primary' ? settingsTheme.colors.primaryHover : settingsTheme.colors.background.lighter};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

// Form Styles
const FormContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.md};
  margin: ${settingsTheme.spacing.md} 0;
`;

const FormRow = styled.div`
  display: flex;
  gap: ${settingsTheme.spacing.md};

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const FormField = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.xs};
  flex: 1;
`;

const FormLabel = styled.label`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.primary};
`;

const FormInput = styled.input<{ hasError?: boolean }>`
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  border: 1px solid ${props => props.hasError ? settingsTheme.colors.error : settingsTheme.colors.border};
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  background-color: ${settingsTheme.colors.background.main};
  color: ${settingsTheme.colors.text.primary};
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.hasError ? settingsTheme.colors.error : settingsTheme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.hasError ? settingsTheme.colors.error + '20' : settingsTheme.colors.primary + '20'};
  }

  &::placeholder {
    color: ${settingsTheme.colors.text.tertiary};
  }
`;

const FormError = styled.span`
  font-size: ${settingsTheme.typography.fontSizes.xs};
  color: ${settingsTheme.colors.error};
  margin-top: ${settingsTheme.spacing.xs};
`;

const RequiredIndicator = styled.span`
  color: ${settingsTheme.colors.error};
  margin-left: 2px;
`;

interface Admin {
  id: number;
  userId: number;
  organizationId: number;
  assignedAt: string;
  isActive: boolean;
  user: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
    userRole: {
      id: number;
      name: string;
    };
  };
  assignedByUser?: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
  };
}

interface Organization {
  id: number;
  name: string;
}

export default function OrganizationAdminPage() {
  const params = useParams();
  const organizationId = params.id as string;

  const [admins, setAdmins] = useState<Admin[]>([]);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [addingAdmin, setAddingAdmin] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Filter admins based on search term
  const filteredAdmins = admins.filter(admin =>
    admin.user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    admin.user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    admin.user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const fetchAdmins = async () => {
    setLoading(true);
    setError(null);

    try {
      const token = getCookie('token');
      if (!token) {
        setError('Authentication required');
        return;
      }

      const response = await fetch(`/api/v1/organization-admin?organizationId=${organizationId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch admins');
      }

      const data = await response.json();
      setAdmins(data.admins || []);
    } catch (err) {
      console.error('Error fetching admins:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch admins');
    } finally {
      setLoading(false);
    }
  };

  const fetchOrganization = async () => {
    try {
      const token = getCookie('token');
      if (!token) return;

      const response = await fetch(`/api/v1/organization?id=${organizationId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setOrganization(data.organization);
      }
    } catch (err) {
      console.error('Error fetching organization:', err);
    }
  };

  useEffect(() => {
    if (organizationId) {
      fetchAdmins();
      fetchOrganization();
    }
  }, [organizationId]);

  const handleRemoveAdmin = async (adminId: number) => {
    if (!confirm('Are you sure you want to remove this admin?')) {
      return;
    }

    try {
      const token = getCookie('token');
      if (!token) {
        setError('Authentication required');
        return;
      }

      const response = await fetch(`/api/v1/organization-admin?id=${adminId}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to remove admin');
      }

      // Refresh the admins list
      fetchAdmins();
    } catch (err) {
      console.error('Error removing admin:', err);
      setError(err instanceof Error ? err.message : 'Failed to remove admin');
    }
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      errors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      errors.lastName = 'Last name is required';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.password) {
      errors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
    }

    if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAddAdmin = async () => {
    if (!validateForm()) return;

    setAddingAdmin(true);
    try {
      const token = getCookie('token');
      if (!token) {
        setError('Authentication required');
        return;
      }

      const response = await fetch('/api/v1/organization-admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          organizationId: Number(organizationId),
          createUser: {
            firstName: formData.firstName.trim(),
            lastName: formData.lastName.trim(),
            email: formData.email.trim(),
            phone: formData.phone.trim() || null,
            password: formData.password,
          },
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create admin');
      }

      // Reset modal state
      handleCloseAddModal();

      // Refresh admins list
      fetchAdmins();
    } catch (err) {
      console.error('Error creating admin:', err);
      setError(err instanceof Error ? err.message : 'Failed to create admin');
    } finally {
      setAddingAdmin(false);
    }
  };

  const handleOpenAddModal = () => {
    setShowAddModal(true);
    // Reset form data
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
    });
    setFormErrors({});
  };

  const handleCloseAddModal = () => {
    setShowAddModal(false);
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
    });
    setFormErrors({});
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear error for this field when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  return (
    <SettingsLayout>
      <AdminContainer>
        <Header>
          <HeaderLeft>
            <BackButton href={`/settings/organizations/${organizationId}/department`}>
              <ArrowLeft size={16} />
              Back to Departments
            </BackButton>
            <Title>
              <UserCheck size={24} />
              Organization Admins
              {organization && ` - ${organization.name}`}
            </Title>
          </HeaderLeft>
          <AddButton onClick={handleOpenAddModal} disabled={loading}>
            <Plus size={16} />
            Add Admin
          </AddButton>
        </Header>

        {error && (
          <ErrorContainer>
            <AlertCircle size={16} />
            {error}
          </ErrorContainer>
        )}

        <SearchContainer>
          <SearchIcon />
          <SearchInput
            type="text"
            placeholder="Search admins by name or email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </SearchContainer>

        {loading ? (
          <LoadingContainer>
            <Loader2 size={24} className="animate-spin" />
          </LoadingContainer>
        ) : filteredAdmins.length > 0 ? (
          <AdminGrid>
            {filteredAdmins.map(admin => (
              <AdminCard key={admin.id}>
                <AdminInfo>
                  {admin.user.imageUrl ? (
                    <AdminImage src={admin.user.imageUrl} alt={`${admin.user.firstName} ${admin.user.lastName}`} />
                  ) : (
                    <AdminAvatar>
                      {getInitials(admin.user.firstName, admin.user.lastName)}
                    </AdminAvatar>
                  )}
                  <AdminDetails>
                    <AdminName>{admin.user.firstName} {admin.user.lastName}</AdminName>
                    <AdminEmail>{admin.user.email}</AdminEmail>
                  </AdminDetails>
                </AdminInfo>

                <AdminMeta>
                  <MetaItem>
                    <MetaLabel>Role:</MetaLabel>
                    <MetaValue>{admin.user.userRole.name}</MetaValue>
                  </MetaItem>
                  <MetaItem>
                    <MetaLabel>Assigned:</MetaLabel>
                    <MetaValue>{formatDate(admin.assignedAt)}</MetaValue>
                  </MetaItem>
                  {admin.assignedByUser && (
                    <MetaItem>
                      <MetaLabel>Assigned by:</MetaLabel>
                      <MetaValue>{admin.assignedByUser.firstName} {admin.assignedByUser.lastName}</MetaValue>
                    </MetaItem>
                  )}
                </AdminMeta>

                <AdminActions>
                  <ActionButton
                    variant="danger"
                    onClick={() => handleRemoveAdmin(admin.id)}
                  >
                    <UserX size={16} />
                    Remove
                  </ActionButton>
                </AdminActions>
              </AdminCard>
            ))}
          </AdminGrid>
        ) : (
          <EmptyState>
            <EmptyIcon />
            <EmptyTitle>No Admins Found</EmptyTitle>
            <EmptyDescription>
              {searchTerm 
                ? `No admins match your search "${searchTerm}". Try a different search term.`
                : 'This organization doesn\'t have any admins yet. Click "Add Admin" to assign your first admin.'
              }
            </EmptyDescription>
          </EmptyState>
        )}
      </AdminContainer>

      {/* Create Admin Modal */}
      {showAddModal && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Create New Admin</ModalTitle>
              <CloseButton onClick={handleCloseAddModal}>&times;</CloseButton>
            </ModalHeader>

            <FormContainer>
              <FormRow>
                <FormField>
                  <FormLabel>
                    First Name <RequiredIndicator>*</RequiredIndicator>
                  </FormLabel>
                  <FormInput
                    type="text"
                    placeholder="Enter first name"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    hasError={!!formErrors.firstName}
                  />
                  {formErrors.firstName && <FormError>{formErrors.firstName}</FormError>}
                </FormField>

                <FormField>
                  <FormLabel>
                    Last Name <RequiredIndicator>*</RequiredIndicator>
                  </FormLabel>
                  <FormInput
                    type="text"
                    placeholder="Enter last name"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    hasError={!!formErrors.lastName}
                  />
                  {formErrors.lastName && <FormError>{formErrors.lastName}</FormError>}
                </FormField>
              </FormRow>

              <FormField>
                <FormLabel>
                  Email <RequiredIndicator>*</RequiredIndicator>
                </FormLabel>
                <FormInput
                  type="email"
                  placeholder="Enter email address"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  hasError={!!formErrors.email}
                />
                {formErrors.email && <FormError>{formErrors.email}</FormError>}
              </FormField>

              <FormField>
                <FormLabel>Phone Number</FormLabel>
                <FormInput
                  type="tel"
                  placeholder="Enter phone number (optional)"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                />
              </FormField>

              <FormRow>
                <FormField>
                  <FormLabel>
                    Password <RequiredIndicator>*</RequiredIndicator>
                  </FormLabel>
                  <FormInput
                    type="password"
                    placeholder="Enter password"
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    hasError={!!formErrors.password}
                  />
                  {formErrors.password && <FormError>{formErrors.password}</FormError>}
                </FormField>

                <FormField>
                  <FormLabel>
                    Confirm Password <RequiredIndicator>*</RequiredIndicator>
                  </FormLabel>
                  <FormInput
                    type="password"
                    placeholder="Confirm password"
                    value={formData.confirmPassword}
                    onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                    hasError={!!formErrors.confirmPassword}
                  />
                  {formErrors.confirmPassword && <FormError>{formErrors.confirmPassword}</FormError>}
                </FormField>
              </FormRow>
            </FormContainer>

            <ModalActions>
              <ModalButton onClick={handleCloseAddModal}>
                Cancel
              </ModalButton>
              <ModalButton
                variant="primary"
                onClick={handleAddAdmin}
                disabled={addingAdmin}
              >
                {addingAdmin ? (
                  <>
                    <Loader2 size={16} className="animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Plus size={16} />
                    Create Admin
                  </>
                )}
              </ModalButton>
            </ModalActions>
          </ModalContent>
        </Modal>
      )}
    </SettingsLayout>
  );
}
