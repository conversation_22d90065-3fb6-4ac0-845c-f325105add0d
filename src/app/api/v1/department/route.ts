import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';
import { hasOrganizationAdminPrivileges, canAccessDepartment } from '@/lib/permissions';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user ID
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  return { userId: payload.userId };
}

// Helper function to check if user owns the organization that contains the department
async function verifyDepartmentAccess(departmentId: number, userId: number) {
  const department = await prisma.department.findUnique({
    where: { id: departmentId },
    include: { organization: true },
  });

  if (!department) {
    return { error: 'Department not found', status: 404 };
  }

  const hasAccess = await canAccessDepartment(userId, departmentId);
  if (!hasAccess) {
    return { error: 'You do not have permission to modify this department. Admin privileges required.', status: 403 };
  }

  return { department };
}

// Helper function to check if user owns the organization
async function verifyOrganizationAccess(organizationId: number, userId: number) {
  const organization = await prisma.organization.findUnique({
    where: { id: organizationId },
  });

  if (!organization) {
    return { error: 'Organization not found', status: 404 };
  }

  const hasAccess = await hasOrganizationAdminPrivileges(userId, organizationId);
  if (!hasAccess) {
    return {
      error: 'You do not have permission to modify departments in this organization. Admin privileges required.',
      status: 403,
    };
  }

  return { organization };
}

// GET all departments in an organization or a specific department
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const organizationId = url.searchParams.get('organizationId');
    const departmentId = url.searchParams.get('id');

    // Get a specific department
    if (departmentId) {
      const departmentIdNum = Number(departmentId);
      if (isNaN(departmentIdNum)) {
        return NextResponse.json({ error: 'Invalid department ID' }, { status: 400 });
      }

      // Check access
      const accessCheck = await verifyDepartmentAccess(departmentIdNum, auth.userId);
      if ('error' in accessCheck) {
        return NextResponse.json({ error: accessCheck.error }, { status: accessCheck.status });
      }

      const department = await prisma.department.findUnique({
        where: { id: departmentIdNum },
        include: {
          members: {
            where: {
              user: {
                userRoleId: 3, // Filter members with role ID = 2
              },
            },
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                  imageUrl: true,
                  userRoleId: true,
                },
              },
            },
          },
        },
      });

      return NextResponse.json({ department });
    }

    // Get all departments in an organization
    if (organizationId) {
      const orgIdNum = Number(organizationId);
      if (isNaN(orgIdNum)) {
        return NextResponse.json({ error: 'Invalid organization ID' }, { status: 400 });
      }

      // Check access
      const accessCheck = await verifyOrganizationAccess(orgIdNum, auth.userId);
      if ('error' in accessCheck) {
        return NextResponse.json({ error: accessCheck.error }, { status: accessCheck.status });
      }

      const departments = await prisma.department.findMany({
        where: { organizationId: orgIdNum },
        include: {
          _count: {
            select: {
              members: {
                where: {
                  user: {
                    userRoleId: 3, // Count only members with role ID = 2
                  },
                },
              },
            },
          },
        },
      });

      return NextResponse.json({ departments });
    }

    return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
  } catch (error) {
    console.error('Error fetching departments:', error);
    return NextResponse.json({ error: 'Failed to fetch departments' }, { status: 500 });
  }
}

// POST to create a new department
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { organizationId, name, description } = body;

    // Validate required fields
    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }
    if (!name) {
      return NextResponse.json({ error: 'Department name is required' }, { status: 400 });
    }

    // Verify organization access
    const accessCheck = await verifyOrganizationAccess(organizationId, auth.userId);
    if ('error' in accessCheck) {
      return NextResponse.json({ error: accessCheck.error }, { status: accessCheck.status });
    }

    // Create new department
    const newDepartment = await prisma.department.create({
      data: {
        name,
        description,
        organizationId,
      },
    });

    return NextResponse.json(
      {
        message: 'Department created successfully',
        department: newDepartment,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating department:', error);
    return NextResponse.json({ error: 'Failed to create department' }, { status: 500 });
  }
}

// PATCH to update an existing department
export async function PATCH(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { id, name, description } = body;

    // Validate required fields
    if (!id) {
      return NextResponse.json({ error: 'Department ID is required' }, { status: 400 });
    }

    // Verify department access
    const accessCheck = await verifyDepartmentAccess(id, auth.userId);
    if ('error' in accessCheck) {
      return NextResponse.json({ error: accessCheck.error }, { status: accessCheck.status });
    }

    // Prepare update data
    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;

    // Update department
    const updatedDepartment = await prisma.department.update({
      where: { id },
      data: updateData,
    });

    return NextResponse.json({
      message: 'Department updated successfully',
      department: updatedDepartment,
    });
  } catch (error) {
    console.error('Error updating department:', error);
    return NextResponse.json({ error: 'Failed to update department' }, { status: 500 });
  }
}

// DELETE to remove a department
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Get department ID from URL
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id || isNaN(Number(id))) {
      return NextResponse.json({ error: 'Valid department ID is required' }, { status: 400 });
    }

    const departmentId = Number(id);

    // Verify department access
    const accessCheck = await verifyDepartmentAccess(departmentId, auth.userId);
    if ('error' in accessCheck) {
      return NextResponse.json({ error: accessCheck.error }, { status: accessCheck.status });
    }

    // Check if department has members
    const membersCount = await prisma.departmentMember.count({
      where: { departmentId },
    });

    if (membersCount > 0) {
      return NextResponse.json(
        {
          error: 'Cannot delete department with existing members. Please remove all members first.',
        },
        { status: 400 }
      );
    }

    // Delete department
    await prisma.department.delete({
      where: { id: departmentId },
    });

    return NextResponse.json({
      message: 'Department deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting department:', error);
    return NextResponse.json({ error: 'Failed to delete department' }, { status: 500 });
  }
}
