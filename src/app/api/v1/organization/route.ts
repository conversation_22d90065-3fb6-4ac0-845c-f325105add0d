import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';
import { getUserOrganizationAdminPrivileges } from '@/lib/permissions';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user ID
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  return { userId: payload.userId };
}

// Helper function to check if user owns the organization
async function verifyOrganizationOwnership(organizationId: number, userId: number) {
  const organization = await prisma.organization.findUnique({
    where: { id: organizationId },
  });

  if (!organization) {
    return { error: 'Organization not found', status: 404 };
  }

  if (organization.ownerUserId !== userId) {
    return { error: 'You do not have permission to modify this organization', status: 403 };
  }

  return { organization };
}

// GET all organizations where the authenticated user has admin privileges
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const organizationId = url.searchParams.get('id');

    // Get specific organization by ID
    if (organizationId) {
      const orgIdNum = Number(organizationId);
      if (isNaN(orgIdNum)) {
        return NextResponse.json({ error: 'Invalid organization ID' }, { status: 400 });
      }

      const organization = await prisma.organization.findUnique({
        where: { id: orgIdNum },
        include: {
          departments: {
            select: {
              id: true,
              name: true,
              description: true,
            },
          },
        },
      });

      if (!organization) {
        return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
      }

      return NextResponse.json({ organization });
    }

    // Get all organizations where user has admin privileges
    const privileges = await getUserOrganizationAdminPrivileges(auth.userId);

    if (privileges.allAdminOrganizations.length === 0) {
      return NextResponse.json({ organizations: [] });
    }

    const organizations = await prisma.organization.findMany({
      where: {
        id: {
          in: privileges.allAdminOrganizations
        }
      },
      include: {
        departments: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
    });

    return NextResponse.json({ organizations });
  } catch (error) {
    console.error('Error fetching organizations:', error);
    return NextResponse.json({ error: 'Failed to fetch organizations' }, { status: 500 });
  }
}

// POST to create a new organization
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { name, description, imageUrl } = body;

    // Validate required fields
    if (!name) {
      return NextResponse.json({ error: 'Organization name is required' }, { status: 400 });
    }

    // Create new organization
    const newOrganization = await prisma.organization.create({
      data: {
        name,
        description,
        imageUrl,
        ownerUserId: auth.userId,
      },
    });

    return NextResponse.json(
      {
        message: 'Organization created successfully',
        organization: newOrganization,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating organization:', error);
    return NextResponse.json({ error: 'Failed to create organization' }, { status: 500 });
  }
}

// PATCH to update an existing organization
export async function PATCH(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { id, name, description, imageUrl } = body;

    // Validate required fields
    if (!id) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    // Verify ownership
    const ownership = await verifyOrganizationOwnership(id, auth.userId);
    if ('error' in ownership) {
      return NextResponse.json({ error: ownership.error }, { status: ownership.status });
    }

    // Prepare update data
    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (imageUrl !== undefined) updateData.imageUrl = imageUrl;

    // Update organization
    const updatedOrganization = await prisma.organization.update({
      where: { id },
      data: updateData,
    });

    return NextResponse.json({
      message: 'Organization updated successfully',
      organization: updatedOrganization,
    });
  } catch (error) {
    console.error('Error updating organization:', error);
    return NextResponse.json({ error: 'Failed to update organization' }, { status: 500 });
  }
}

// DELETE to remove an organization
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Get organization ID from URL
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id || isNaN(Number(id))) {
      return NextResponse.json({ error: 'Valid organization ID is required' }, { status: 400 });
    }

    const organizationId = Number(id);

    // Verify ownership
    const ownership = await verifyOrganizationOwnership(organizationId, auth.userId);
    if ('error' in ownership) {
      return NextResponse.json({ error: ownership.error }, { status: ownership.status });
    }

    // Check if organization has departments
    const departmentsCount = await prisma.department.count({
      where: { organizationId },
    });

    if (departmentsCount > 0) {
      return NextResponse.json(
        {
          error:
            'Cannot delete organization with existing departments. Please delete all departments first.',
        },
        { status: 400 }
      );
    }

    // Delete organization
    await prisma.organization.delete({
      where: { id: organizationId },
    });

    return NextResponse.json({
      message: 'Organization deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting organization:', error);
    return NextResponse.json({ error: 'Failed to delete organization' }, { status: 500 });
  }
}
