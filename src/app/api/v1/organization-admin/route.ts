import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';
import bcrypt from 'bcrypt';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user info
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  // Get user with role information
  const user = await prisma.user.findUnique({
    where: { id: payload.userId },
    include: { userRole: true },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  return {
    userId: user.id,
    isOwner: user.userRole.isOwner,
    isAdmin: user.userRole.isAdmin,
    isMember: user.userRole.isMember,
  };
}

// Helper function to verify user is owner of the organization
async function verifyOrganizationOwnership(organizationId: number, userId: number) {
  const organization = await prisma.organization.findUnique({
    where: { id: organizationId },
  });

  if (!organization) {
    return { error: 'Organization not found', status: 404 };
  }

  if (organization.ownerUserId !== userId) {
    return {
      error: 'You do not have permission to manage admins in this organization. Owner access required.',
      status: 403,
    };
  }

  return { organization };
}

/**
 * GET API for organization admins
 * 
 * Query parameters:
 * - organizationId: Required. ID of the organization to get admins for
 * - id: Optional. Get specific admin by ID
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const organizationId = url.searchParams.get('organizationId');
    const adminId = url.searchParams.get('id');

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    const orgIdNum = Number(organizationId);
    if (isNaN(orgIdNum)) {
      return NextResponse.json({ error: 'Invalid organization ID' }, { status: 400 });
    }

    // Verify organization ownership
    const ownershipCheck = await verifyOrganizationOwnership(orgIdNum, auth.userId);
    if ('error' in ownershipCheck) {
      return NextResponse.json({ error: ownershipCheck.error }, { status: ownershipCheck.status });
    }

    // Get specific admin
    if (adminId) {
      const adminIdNum = Number(adminId);
      if (isNaN(adminIdNum)) {
        return NextResponse.json({ error: 'Invalid admin ID' }, { status: 400 });
      }

      const admin = await prisma.organizationAdmin.findUnique({
        where: { id: adminIdNum },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              imageUrl: true,
              userRole: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
          assignedByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      if (!admin) {
        return NextResponse.json({ error: 'Admin not found' }, { status: 404 });
      }

      // Verify admin belongs to the requested organization
      if (admin.organizationId !== orgIdNum) {
        return NextResponse.json({ error: 'Admin not found in this organization' }, { status: 404 });
      }

      return NextResponse.json({ admin });
    }

    // Get all admins for the organization
    const admins = await prisma.organizationAdmin.findMany({
      where: {
        organizationId: orgIdNum,
        isActive: true,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
            userRole: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        assignedByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      orderBy: {
        assignedAt: 'desc',
      },
    });

    return NextResponse.json({ admins });
  } catch (error) {
    console.error('Error fetching organization admins:', error);
    return NextResponse.json({ error: 'Failed to fetch organization admins' }, { status: 500 });
  }
}

/**
 * POST API to add a new organization admin
 *
 * Body parameters:
 * - organizationId: Required. ID of the organization
 * - userId: Optional. ID of existing user to make admin
 * - createUser: Optional. Object with user data to create new user
 *   - email: Required. User email
 *   - firstName: Required. User first name
 *   - lastName: Required. User last name
 *   - phone: Optional. User phone number
 *   - password: Required. User password
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { organizationId, userId, createUser } = body;

    // Validate required fields
    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    if (!userId && !createUser) {
      return NextResponse.json({ error: 'Either User ID or createUser data is required' }, { status: 400 });
    }

    if (createUser) {
      const { email, firstName, lastName, password } = createUser;
      if (!email || !firstName || !lastName || !password) {
        return NextResponse.json({
          error: 'Email, firstName, lastName, and password are required for creating new user'
        }, { status: 400 });
      }
    }

    // Verify organization ownership
    const ownershipCheck = await verifyOrganizationOwnership(organizationId, auth.userId);
    if ('error' in ownershipCheck) {
      return NextResponse.json({ error: ownershipCheck.error }, { status: ownershipCheck.status });
    }

    let targetUserId = userId;
    let user = null;

    // If creating new user
    if (createUser) {
      const { email, firstName, lastName, phone, password } = createUser;

      // Check if email already exists
      const existingUser = await prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        return NextResponse.json({ error: 'User with this email already exists' }, { status: 409 });
      }

      // Hash password
      const saltRounds = 10;
      const passwordHash = await bcrypt.hash(password, saltRounds);

      // Create new user with admin role (userRoleId = 2)
      user = await prisma.user.create({
        data: {
          email,
          firstName,
          lastName,
          phone: phone || null,
          passwordHash,
          userRoleId: 2, // Fixed admin role ID
        },
        include: {
          userRole: true,
        },
      });

      targetUserId = user.id;
    } else {
      // Check if existing user exists and has admin role
      user = await prisma.user.findUnique({
        where: { id: userId },
        include: { userRole: true },
      });

      if (!user) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }

      if (!user.userRole.isAdmin) {
        return NextResponse.json({ error: 'User must have admin role to be assigned as organization admin' }, { status: 400 });
      }
    }

    // Check if user is already an admin of this organization
    const existingAdmin = await prisma.organizationAdmin.findUnique({
      where: {
        userId_organizationId: {
          userId: targetUserId,
          organizationId,
        },
      },
    });

    if (existingAdmin) {
      if (existingAdmin.isActive) {
        return NextResponse.json({ error: 'User is already an admin of this organization' }, { status: 409 });
      } else {
        // Reactivate existing admin
        const reactivatedAdmin = await prisma.organizationAdmin.update({
          where: { id: existingAdmin.id },
          data: {
            isActive: true,
            assignedAt: new Date(),
            assignedBy: auth.userId,
          },
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                imageUrl: true,
                userRole: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        });

        return NextResponse.json(
          {
            message: 'User reactivated as organization admin successfully',
            admin: reactivatedAdmin,
          },
          { status: 200 }
        );
      }
    }

    // Create new organization admin
    const newAdmin = await prisma.organizationAdmin.create({
      data: {
        userId: targetUserId,
        organizationId,
        assignedBy: auth.userId,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
            userRole: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json(
      {
        message: createUser
          ? 'User created and assigned as organization admin successfully'
          : 'User assigned as organization admin successfully',
        admin: newAdmin,
        userCreated: !!createUser,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error adding organization admin:', error);
    return NextResponse.json({ error: 'Failed to add organization admin' }, { status: 500 });
  }
}

/**
 * PATCH API to update organization admin status
 *
 * Body parameters:
 * - id: Required. ID of the organization admin to update
 * - isActive: Optional. Update active status
 */
export async function PATCH(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { id, isActive } = body;

    // Validate required fields
    if (!id) {
      return NextResponse.json({ error: 'Admin ID is required' }, { status: 400 });
    }

    // Get the admin to update
    const admin = await prisma.organizationAdmin.findUnique({
      where: { id },
      include: {
        organization: true,
      },
    });

    if (!admin) {
      return NextResponse.json({ error: 'Organization admin not found' }, { status: 404 });
    }

    // Verify organization ownership
    const ownershipCheck = await verifyOrganizationOwnership(admin.organizationId, auth.userId);
    if ('error' in ownershipCheck) {
      return NextResponse.json({ error: ownershipCheck.error }, { status: ownershipCheck.status });
    }

    // Prepare update data
    const updateData: any = {};
    if (isActive !== undefined) updateData.isActive = isActive;

    // Update admin
    const updatedAdmin = await prisma.organizationAdmin.update({
      where: { id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
            userRole: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: 'Organization admin updated successfully',
      admin: updatedAdmin,
    });
  } catch (error) {
    console.error('Error updating organization admin:', error);
    return NextResponse.json({ error: 'Failed to update organization admin' }, { status: 500 });
  }
}

/**
 * DELETE API to remove organization admin
 *
 * Query parameters:
 * - id: Required. ID of the organization admin to remove
 */
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const adminId = url.searchParams.get('id');

    if (!adminId || isNaN(Number(adminId))) {
      return NextResponse.json({ error: 'Valid admin ID is required' }, { status: 400 });
    }

    const adminIdNum = Number(adminId);

    // Get the admin to delete
    const admin = await prisma.organizationAdmin.findUnique({
      where: { id: adminIdNum },
      include: {
        organization: true,
      },
    });

    if (!admin) {
      return NextResponse.json({ error: 'Organization admin not found' }, { status: 404 });
    }

    // Verify organization ownership
    const ownershipCheck = await verifyOrganizationOwnership(admin.organizationId, auth.userId);
    if ('error' in ownershipCheck) {
      return NextResponse.json({ error: ownershipCheck.error }, { status: ownershipCheck.status });
    }

    // Soft delete by setting isActive to false
    await prisma.organizationAdmin.update({
      where: { id: adminIdNum },
      data: { isActive: false },
    });

    return NextResponse.json({
      message: 'Organization admin removed successfully',
    });
  } catch (error) {
    console.error('Error removing organization admin:', error);
    return NextResponse.json({ error: 'Failed to remove organization admin' }, { status: 500 });
  }
}
