import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';
import { hasOrganizationAdminPrivileges, canAccessDepartment } from '@/lib/permissions';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user ID
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  return { userId: payload.userId };
}

// Helper function to check if user has admin access to the organization
async function verifyOrganizationAccess(organizationId: number, userId: number) {
  const organization = await prisma.organization.findUnique({
    where: { id: organizationId },
  });

  if (!organization) {
    return { error: 'Organization not found', status: 404 };
  }

  const hasAccess = await hasOrganizationAdminPrivileges(userId, organizationId);
  if (!hasAccess) {
    return {
      error: 'You do not have permission to modify members in this organization. Admin privileges required.',
      status: 403,
    };
  }

  return { organization };
}

// Helper function to check if user has admin access to the department's organization
async function verifyDepartmentAccess(departmentId: number, userId: number) {
  const department = await prisma.department.findUnique({
    where: { id: departmentId },
    include: { organization: true },
  });

  if (!department) {
    return { error: 'Department not found', status: 404 };
  }

  const hasAccess = await canAccessDepartment(userId, departmentId);
  if (!hasAccess) {
    return {
      error: 'You do not have permission to modify members in this department. Admin privileges required.',
      status: 403,
    };
  }

  return { department };
}

// Helper function to check if a user is a member of the specified department
async function isUserDepartmentMember(userId: number, departmentId: number) {
  const member = await prisma.departmentMember.findFirst({
    where: {
      userId,
      departmentId,
    },
  });

  return !!member;
}

// GET members in an organization or department
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const organizationId = url.searchParams.get('organizationId');
    const departmentId = url.searchParams.get('departmentId');
    const memberId = url.searchParams.get('id');
    const nameSearch = url.searchParams.get('name');

    // Get a specific member
    if (memberId) {
      const memberIdNum = Number(memberId);
      if (isNaN(memberIdNum)) {
        return NextResponse.json({ error: 'Invalid member ID' }, { status: 400 });
      }

      const member = await prisma.departmentMember.findUnique({
        where: { id: memberIdNum },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              imageUrl: true,
              userRoleId: true,
              deletedAt: true,
            },
          },
          department: {
            include: {
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });

      if (!member) {
        return NextResponse.json({ error: 'Member not found' }, { status: 404 });
      }

      // Check if requesting user has access to this member's department
      const accessCheck = await verifyDepartmentAccess(member.departmentId, auth.userId);
      if ('error' in accessCheck) {
        return NextResponse.json({ error: accessCheck.error }, { status: accessCheck.status });
      }

      return NextResponse.json({ member });
    }

    // Get members by department
    if (departmentId) {
      const deptIdNum = Number(departmentId);
      if (isNaN(deptIdNum)) {
        return NextResponse.json({ error: 'Invalid department ID' }, { status: 400 });
      }

      // Check access
      const accessCheck = await verifyDepartmentAccess(deptIdNum, auth.userId);
      if ('error' in accessCheck) {
        return NextResponse.json({ error: accessCheck.error }, { status: accessCheck.status });
      }

      // Get members with role ID 2 (as specified in requirements), including deleted users
      const whereClause: any = {
        departmentId: deptIdNum,
        user: {
          userRoleId: 3, // Filter by role ID 2
          // Removed deletedAt filter to include deleted users
        },
      };

      // Add name search filter if provided
      if (nameSearch) {
        whereClause.user.OR = [
          {
            firstName: {
              contains: nameSearch,
              mode: 'insensitive',
            },
          },
          {
            lastName: {
              contains: nameSearch,
              mode: 'insensitive',
            },
          },
          {
            email: {
              contains: nameSearch,
              mode: 'insensitive',
            },
          },
        ];
      }

      const members = await prisma.departmentMember.findMany({
        where: whereClause,
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              imageUrl: true,
              userRoleId: true,
              deletedAt: true,
            },
          },
          department: {
            include: {
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });

      return NextResponse.json({ members });
    }

    // Get members by organization
    if (organizationId) {
      const orgIdNum = Number(organizationId);
      if (isNaN(orgIdNum)) {
        return NextResponse.json({ error: 'Invalid organization ID' }, { status: 400 });
      }

      // Check access
      const accessCheck = await verifyOrganizationAccess(orgIdNum, auth.userId);
      if ('error' in accessCheck) {
        return NextResponse.json({ error: accessCheck.error }, { status: accessCheck.status });
      }

      // Get all departments in the organization
      const departments = await prisma.department.findMany({
        where: { organizationId: orgIdNum },
        select: { id: true },
      });

      const departmentIds = departments.map(dept => dept.id);

      // Get members with role ID 2 across all departments in the organization (including deleted users)
      const whereClause: any = {
        departmentId: { in: departmentIds },
        user: {
          userRoleId: 3, // Filter by role ID 2
          // Removed deletedAt filter to include deleted users
        },
      };

      // Add name search filter if provided
      if (nameSearch) {
        whereClause.user.OR = [
          {
            firstName: {
              contains: nameSearch,
              mode: 'insensitive',
            },
          },
          {
            lastName: {
              contains: nameSearch,
              mode: 'insensitive',
            },
          },
          {
            email: {
              contains: nameSearch,
              mode: 'insensitive',
            },
          },
        ];
      }

      const members = await prisma.departmentMember.findMany({
        where: whereClause,
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              imageUrl: true,
              userRoleId: true,
              deletedAt: true,
            },
          },
          department: {
            include: {
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });

      return NextResponse.json({ members });
    }

    return NextResponse.json(
      { error: 'Organization ID or Department ID is required' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error fetching members:', error);
    return NextResponse.json({ error: 'Failed to fetch members' }, { status: 500 });
  }
}

// POST to add a user as a member to a department or register a new user
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { departmentId, email, password, firstName, lastName, phone, imageUrl, isLeader } = body;

    // Validate required fields
    if (!departmentId) {
      return NextResponse.json({ error: 'Department ID is required' }, { status: 400 });
    }

    // Verify department access
    const accessCheck = await verifyDepartmentAccess(departmentId, auth.userId);
    if ('error' in accessCheck) {
      return NextResponse.json({ error: accessCheck.error }, { status: accessCheck.status });
    }

    // Validate required fields for new user
    if (!email || !password || !firstName || !lastName) {
      return NextResponse.json(
        {
          error: 'Email, password, first name, and last name are required',
        },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json(
        {
          error: 'User with this email already exists',
        },
        { status: 409 }
      );
    }

    // Hash the password
    const saltRounds = 10;
    const bcrypt = require('bcrypt');
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create new user with role ID 2
    const user = await prisma.user.create({
      data: {
        email,
        passwordHash,
        firstName,
        lastName,
        phone,
        imageUrl,
        userRoleId: 3, // Set role ID to 2 as specified in requirements
      },
    });

    // Add user to department
    const newMember = await prisma.departmentMember.create({
      data: {
        userId: user.id,
        departmentId,
        isLeader: isLeader || false,
        joinedAt: new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
            userRoleId: true,
          },
        },
      },
    });

    return NextResponse.json(
      {
        message: 'User registered and added as member successfully',
        member: newMember,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error adding member:', error);
    return NextResponse.json({ error: 'Failed to add member' }, { status: 500 });
  }
}

// PATCH to update a member
export async function PATCH(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { id, departmentId, isLeader } = body;

    // Validate required fields
    if (!id) {
      return NextResponse.json({ error: 'Member ID is required' }, { status: 400 });
    }

    // Get the member to update
    const member = await prisma.departmentMember.findUnique({
      where: { id },
      include: {
        department: true,
      },
    });

    if (!member) {
      return NextResponse.json({ error: 'Member not found' }, { status: 404 });
    }

    // Check if the authenticated user has access to the member's current department
    const currentDeptAccessCheck = await verifyDepartmentAccess(member.departmentId, auth.userId);
    if ('error' in currentDeptAccessCheck) {
      return NextResponse.json(
        { error: currentDeptAccessCheck.error },
        { status: currentDeptAccessCheck.status }
      );
    }

    // If changing department, verify access to the new department
    if (departmentId && departmentId !== member.departmentId) {
      const newDeptAccessCheck = await verifyDepartmentAccess(departmentId, auth.userId);
      if ('error' in newDeptAccessCheck) {
        return NextResponse.json(
          { error: newDeptAccessCheck.error },
          { status: newDeptAccessCheck.status }
        );
      }
    }

    // Prepare update data
    const updateData: any = {};
    if (departmentId !== undefined) updateData.departmentId = departmentId;
    if (isLeader !== undefined) updateData.isLeader = isLeader;

    // Update member
    const updatedMember = await prisma.departmentMember.update({
      where: { id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
            userRoleId: true,
          },
        },
        department: true,
      },
    });

    return NextResponse.json({
      message: 'Member updated successfully',
      member: updatedMember,
    });
  } catch (error) {
    console.error('Error updating member:', error);
    return NextResponse.json({ error: 'Failed to update member' }, { status: 500 });
  }
}

// DELETE to remove a member from a department
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Get member ID from URL
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id || isNaN(Number(id))) {
      return NextResponse.json({ error: 'Valid member ID is required' }, { status: 400 });
    }

    const memberId = Number(id);

    // Get the member to delete
    const member = await prisma.departmentMember.findUnique({
      where: { id: memberId },
      include: {
        department: true,
      },
    });

    if (!member) {
      return NextResponse.json({ error: 'Member not found' }, { status: 404 });
    }

    // Verify department access
    const accessCheck = await verifyDepartmentAccess(member.departmentId, auth.userId);
    if ('error' in accessCheck) {
      return NextResponse.json({ error: accessCheck.error }, { status: accessCheck.status });
    }

    // Check if this is the only department the user is a member of
    const userMembershipCount = await prisma.departmentMember.count({
      where: {
        userId: member.userId,
      },
    });

    // Delete member from department
    await prisma.departmentMember.delete({
      where: { id: memberId },
    });

    // If this was the user's only department, reset their role to default (assuming 1 is default)
    if (userMembershipCount === 1) {
      await prisma.user.update({
        where: { id: member.userId },
        data: { userRoleId: 1 }, // Reset to default role
      });
    }

    return NextResponse.json({
      message: 'Member removed successfully',
    });
  } catch (error) {
    console.error('Error removing member:', error);
    return NextResponse.json({ error: 'Failed to remove member' }, { status: 500 });
  }
}
