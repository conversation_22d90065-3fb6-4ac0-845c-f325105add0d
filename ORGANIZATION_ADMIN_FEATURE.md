# Organization Admin Feature

## Overview

This feature adds the ability for organization owners to assign admin privileges to users within their organizations. Admins can manage the organization's departments, tasks, and members, but cannot manage other admins (only owners can do that).

## Key Features

### 1. Multi-Organization Admin Support
- A single user can be an admin of multiple organizations
- Admin privileges are organization-specific
- Admins have access to all departments within their assigned organizations

### 2. Permission Hierarchy
```
Owner > Admin > Member
```

- **Owner**: Can manage everything including admins
- **Admin**: Can manage departments, tasks, and members (but not other admins)
- **Member**: Can only access assigned tasks and departments

### 3. Admin Management Interface
- Accessible via Settings > Organizations > [Organization] > Manage Admins
- Only organization owners can access this interface
- Shows list of current admins with assignment details
- Create new admin users directly with fixed role ID = 2
- Remove existing admins

## Database Schema Changes

### New Model: OrganizationAdmin
```prisma
model OrganizationAdmin {
  id             Int          @id @default(autoincrement())
  userId         Int          @map("user_id")
  organizationId Int          @map("organization_id")
  assignedAt     DateTime     @default(now()) @map("assigned_at")
  assignedBy     Int?         @map("assigned_by")
  isActive       Boolean      @default(true) @map("is_active")
  createdAt      DateTime     @default(now()) @map("created_at")
  updatedAt      DateTime     @updatedAt @map("updated_at")
  
  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id])
  user           User         @relation(fields: [userId], references: [id])
  assignedByUser User?        @relation("AssignedAdminBy", fields: [assignedBy], references: [id])

  @@unique([userId, organizationId])
  @@map("organization_admins")
}
```

### Updated Relations
- User model now includes `organizationAdmins` and `assignedAdmins` relations
- Organization model now includes `organizationAdmins` relation

## API Endpoints

### 1. Organization Admin Management
- `GET /api/v1/organization-admin?organizationId={id}` - Get all admins for an organization
- `GET /api/v1/organization-admin?id={adminId}` - Get specific admin details
- `POST /api/v1/organization-admin` - Add existing user as admin OR create new user as admin
  - Body for existing user: `{ organizationId, userId }`
  - Body for new user: `{ organizationId, createUser: { email, firstName, lastName, phone?, password } }`
- `PATCH /api/v1/organization-admin` - Update admin status
- `DELETE /api/v1/organization-admin?id={adminId}` - Remove admin

### 2. Available Users
- `GET /api/v1/organization-admin/available-users?organizationId={id}&search={term}` - Get users that can be assigned as admins

### 3. Updated Existing APIs
- `/api/v1/organization` - Now returns organizations where user has admin privileges
- `/api/v1/me` - Now includes admin organization information
- `/api/v1/task` - Updated permission checking to include admin privileges
- `/api/v1/department` - Updated permission checking to include admin privileges
- `/api/v1/member` - Updated permission checking to include admin privileges

## Permission System

### New Permission Helper Functions
Located in `/src/lib/permissions.ts`:

- `isOrganizationOwner(userId, organizationId)` - Check if user owns organization
- `isOrganizationAdmin(userId, organizationId)` - Check if user is admin of organization
- `hasOrganizationAdminPrivileges(userId, organizationId)` - Check if user has admin privileges (owner or admin)
- `getUserOrganizationAdminPrivileges(userId)` - Get all organizations where user has admin privileges
- `canAccessDepartment(userId, departmentId)` - Check if user can access department
- `canAccessTask(userId, taskId)` - Check if user can access task

### Permission Checking Flow
1. Check if user is organization owner (highest privilege)
2. If not owner, check if user is organization admin
3. For department/task access, check organization-level privileges first
4. Fall back to direct assignment/membership checks

## UI Components

### 1. Admin Management Page
- **Location**: `/src/app/(main)/settings/organizations/[id]/admin/page.tsx`
- **Features**:
  - List all organization admins
  - Search functionality
  - Add new admin modal
  - Remove admin functionality
  - Admin details display

### 2. Admin Management Button
- **Location**: Added to department management page
- **Path**: `/src/app/(main)/settings/organizations/[id]/department/page.tsx`
- **Button**: "Manage Admins" - visible only to organization owners

### 3. Create Admin Modal
- **Features**:
  - Create new user form with validation
  - Fixed user role ID = 2 (admin role)
  - Required fields: firstName, lastName, email, password
  - Optional field: phone
  - Password confirmation validation
  - Email format validation
  - Automatic assignment to organization_admins

## Usage Instructions

### For Organization Owners

1. **Access Admin Management**:
   - Go to Settings > Organizations
   - Click on your organization
   - Click "Manage Admins" button

2. **Create Admin**:
   - Click "Add Admin" button
   - Fill in the user creation form:
     - First Name (required)
     - Last Name (required)
     - Email (required, must be unique)
     - Phone (optional)
     - Password (required, min 6 characters)
     - Confirm Password (required, must match)
   - Click "Create Admin" to confirm

3. **Remove Admin**:
   - Find the admin in the list
   - Click "Remove" button
   - Confirm the removal

### For Organization Admins

1. **Access Organization**:
   - Login to your account
   - Go to Settings > Organizations
   - You'll see organizations where you're an admin

2. **Manage Organization**:
   - Access all departments in the organization
   - Create/edit/delete departments
   - Manage department members
   - Create and assign tasks

## Security Considerations

1. **Owner-Only Admin Management**: Only organization owners can add/remove admins
2. **Role Validation**: Only users with admin role can be assigned as organization admins
3. **Permission Inheritance**: Admins inherit organization-level permissions
4. **Soft Delete**: Admin assignments are soft-deleted (isActive flag) for audit trail
5. **Token-Based Authentication**: All API endpoints require valid JWT tokens

## Testing

### Manual Testing Steps
1. Create an organization as owner
2. Create a user with admin role
3. Assign the user as organization admin
4. Login as the admin user
5. Verify admin can access organization features
6. Verify admin cannot manage other admins
7. Remove admin assignment as owner
8. Verify admin loses access

### API Testing
Use the provided test script: `test-organization-admin.js`

## Migration Notes

- The feature uses existing user roles (isAdmin flag in UserRole)
- No changes to existing user data required
- New OrganizationAdmin table will be empty initially
- Existing organization owners retain all privileges
- Backward compatibility maintained for all existing APIs

## Future Enhancements

1. **Granular Permissions**: Department-specific admin roles
2. **Admin Notifications**: Notify users when assigned/removed as admin
3. **Admin Activity Logs**: Track admin actions for audit purposes
4. **Bulk Admin Management**: Assign multiple admins at once
5. **Admin Expiration**: Time-limited admin assignments
