# Organization Admin Feature - Testing Guide

## Overview

This guide provides step-by-step instructions for testing the Organization Admin feature, including both manual UI testing and API testing.

## Prerequisites

1. **Development Server Running**
   ```bash
   npm run dev
   ```
   Server should be accessible at `http://localhost:3000`

2. **Database Setup**
   - Ensure PostgreSQL is running
   - Database migrations are applied
   - At least one organization exists
   - At least one user with owner role exists

3. **Authentication**
   - You need valid JWT tokens for testing
   - Owner token for admin management
   - Admin token for testing admin privileges

## Manual UI Testing

### Test 1: Access Admin Management Page

1. **Login as Organization Owner**
   - Go to `http://localhost:3000/login`
   - Login with owner credentials

2. **Navigate to Admin Management**
   - Go to Settings > Organizations
   - Click on an organization you own
   - Click "Manage Admins" button
   - Verify you can access `/settings/organizations/[id]/admin`

3. **Expected Results**
   - Page loads successfully
   - Shows "Organization Admins" title
   - Shows "Add Admin" button
   - Shows empty state or existing admins list

### Test 2: Create New Admin User

1. **Open Create Admin Modal**
   - Click "Add Admin" button
   - Mo<PERSON> should open with title "Create New Admin"

2. **Test Form Validation**
   - Try submitting empty form → Should show validation errors
   - Enter invalid email → Should show email format error
   - Enter password less than 6 characters → Should show password length error
   - Enter mismatched passwords → Should show password mismatch error

3. **Create Valid Admin**
   - Fill in all required fields:
     - First Name: "Test"
     - Last Name: "Admin"
     - Email: "<EMAIL>" (must be unique)
     - Phone: "+1234567890" (optional)
     - Password: "password123"
     - Confirm Password: "password123"
   - Click "Create Admin"

4. **Expected Results**
   - Modal shows loading state
   - Success message appears
   - Modal closes
   - New admin appears in the list
   - Admin has role "Admin" (userRoleId = 2)

### Test 3: Admin List Display

1. **Verify Admin Information**
   - Admin card shows correct name and email
   - Shows assignment date
   - Shows "Assigned by" information
   - Shows "Remove" button

2. **Test Search Functionality**
   - Use search box to filter admins
   - Search by name or email
   - Verify filtering works correctly

### Test 4: Remove Admin

1. **Remove Admin**
   - Click "Remove" button on an admin
   - Confirm removal in dialog
   - Verify admin is removed from list

2. **Expected Results**
   - Admin disappears from list
   - Success message appears

### Test 5: Admin User Login

1. **Login as Created Admin**
   - Logout from owner account
   - Login with admin credentials (email and password from Test 2)

2. **Test Admin Privileges**
   - Go to Settings > Organizations
   - Verify admin can see the organization they're assigned to
   - Go to organization departments
   - Verify admin can manage departments and members
   - Verify admin CANNOT access "Manage Admins" (owner only)

## API Testing

### Setup API Testing

1. **Get JWT Tokens**
   ```bash
   # Login as owner
   curl -X POST http://localhost:3000/api/v1/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"password"}'
   
   # Copy the token from response
   ```

2. **Set Environment Variables**
   ```bash
   export OWNER_TOKEN="your-owner-jwt-token"
   export ORG_ID="1"  # Replace with actual organization ID
   ```

### API Test Cases

#### Test 1: Create New Admin User
```bash
curl -X POST http://localhost:3000/api/v1/organization-admin \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $OWNER_TOKEN" \
  -d '{
    "organizationId": 1,
    "createUser": {
      "email": "<EMAIL>",
      "firstName": "API",
      "lastName": "Admin",
      "phone": "+1234567890",
      "password": "password123"
    }
  }'
```

**Expected Response:**
```json
{
  "message": "User created and assigned as organization admin successfully",
  "admin": {
    "id": 1,
    "userId": 2,
    "organizationId": 1,
    "user": {
      "firstName": "API",
      "lastName": "Admin",
      "email": "<EMAIL>"
    }
  },
  "userCreated": true
}
```

#### Test 2: Get Organization Admins
```bash
curl -X GET "http://localhost:3000/api/v1/organization-admin?organizationId=1" \
  -H "Authorization: Bearer $OWNER_TOKEN"
```

#### Test 3: Remove Admin
```bash
curl -X DELETE "http://localhost:3000/api/v1/organization-admin?id=1" \
  -H "Authorization: Bearer $OWNER_TOKEN"
```

#### Test 4: Test Permission Validation
```bash
# Try to create admin without authentication
curl -X POST http://localhost:3000/api/v1/organization-admin \
  -H "Content-Type: application/json" \
  -d '{"organizationId": 1, "createUser": {"email": "<EMAIL>"}}'

# Expected: 401 Authentication required
```

#### Test 5: Test Email Uniqueness
```bash
# Try to create admin with existing email
curl -X POST http://localhost:3000/api/v1/organization-admin \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $OWNER_TOKEN" \
  -d '{
    "organizationId": 1,
    "createUser": {
      "email": "<EMAIL>",
      "firstName": "Duplicate",
      "lastName": "User",
      "password": "password123"
    }
  }'

# Expected: 409 User with this email already exists
```

## Database Verification

### Check Created Users
```sql
-- Check if user was created with correct role
SELECT u.id, u.email, u.firstName, u.lastName, u.userRoleId, ur.name as roleName
FROM users u
JOIN user_roles ur ON u.userRoleId = ur.id
WHERE u.email = '<EMAIL>';

-- Expected: userRoleId = 2, roleName = 'Admin'
```

### Check Organization Admin Assignment
```sql
-- Check if user was assigned to organization
SELECT oa.*, u.email, o.name as orgName
FROM organization_admins oa
JOIN users u ON oa.userId = u.id
JOIN organizations o ON oa.organizationId = o.id
WHERE u.email = '<EMAIL>';

-- Expected: Record exists with isActive = true
```

## Common Issues and Troubleshooting

### Issue 1: "hasError prop" Warning
- **Problem**: React warning about unknown DOM prop
- **Solution**: Fixed with `shouldForwardProp` in styled-components
- **Verification**: No console warnings in browser

### Issue 2: Authentication Errors
- **Problem**: 401 errors when testing API
- **Solution**: Ensure valid JWT token in Authorization header
- **Check**: Token not expired, user has correct permissions

### Issue 3: Email Already Exists
- **Problem**: 409 error when creating admin
- **Solution**: Use unique email addresses for testing
- **Check**: Database for existing users with same email

### Issue 4: Permission Denied
- **Problem**: 403 error when accessing admin features
- **Solution**: Ensure user is organization owner
- **Check**: User's role and organization ownership

## Success Criteria

✅ **All tests should pass with these results:**

1. **UI Tests**
   - Admin management page loads correctly
   - Create admin form works with validation
   - Admin list displays correctly
   - Remove admin functionality works
   - Admin user can login and access organization

2. **API Tests**
   - All endpoints return expected responses
   - Authentication and authorization work correctly
   - Data validation prevents invalid inputs
   - Database records are created correctly

3. **Security Tests**
   - Only owners can manage admins
   - Admins cannot manage other admins
   - Password hashing works correctly
   - Email uniqueness is enforced

4. **Integration Tests**
   - Created admin can access organization features
   - Permission system works across all APIs
   - UI and API are synchronized

## Performance Considerations

- Form validation should be responsive (< 100ms)
- API responses should be fast (< 500ms)
- Page loads should be quick (< 2s)
- No memory leaks in modal operations
